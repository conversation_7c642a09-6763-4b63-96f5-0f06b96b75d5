[workspace]
resolver = "2"
members = [
    "crates",
    "crates/adapters/*",
    "crates/analysis",
    "crates/backtest",
    "crates/cli",
    "crates/common",
    "crates/core",
    "crates/cryptography",
    "crates/data",
    "crates/execution",
    "crates/indicators",
    "crates/infrastructure",
    "crates/live",
    "crates/model",
    "crates/network",
    "crates/persistence",
    "crates/portfolio",
    "crates/pyo3",
    "crates/risk",
    "crates/serialization",
    "crates/system",
    "crates/testkit",
    "crates/trading",
]

[workspace.package]
version = "0.49.0"
edition = "2024"
rust-version = "1.87.0"
authors = ["Nautech Systems <<EMAIL>>"]
license = "LGPL-3.0"
readme = "README.md"
description = "A high-performance algorithmic trading platform and event-driven backtester"
categories = ["finance", "simulation", "asynchronous"]
keywords = ["finance", "trading", "trading-platform", "algorithmic-trading", "quantitative-finance"]
documentation = "https://nautilustrader.io/docs"
repository = "https://github.com/nautechsystems/nautilus_trader"
homepage = "https://nautilustrader.io"

[workspace.dependencies]
nautilus-analysis = { path = "crates/analysis", version = "0.49.0" }
nautilus-backtest = { path = "crates/backtest", version = "0.49.0" }
nautilus-cli = { path = "crates/cli", version = "0.49.0" }
nautilus-common = { path = "crates/common", version = "0.49.0" }
nautilus-core = { path = "crates/core", version = "0.49.0" }
nautilus-cryptography = { path = "crates/cryptography", version = "0.49.0" }
nautilus-data = { path = "crates/data", version = "0.49.0" }
nautilus-execution = { path = "crates/execution", version = "0.49.0" }
nautilus-indicators = { path = "crates/indicators", version = "0.49.0" }
nautilus-infrastructure = { path = "crates/infrastructure", version = "0.49.0" }
nautilus-live = { path = "crates/live", version = "0.49.0" }
nautilus-model = { path = "crates/model", version = "0.49.0" }
nautilus-network = { path = "crates/network", version = "0.49.0" }
nautilus-persistence = { path = "crates/persistence", version = "0.49.0" }
nautilus-portfolio = { path = "crates/portfolio", version = "0.49.0" }
nautilus-pyo3 = { path = "crates/pyo3", version = "0.49.0" }
nautilus-risk = { path = "crates/risk", version = "0.49.0" }
nautilus-serialization = { path = "crates/serialization", version = "0.49.0" }
nautilus-system = { path = "crates/system", version = "0.49.0" }
nautilus-testkit = { path = "crates/testkit", version = "0.49.0" }
nautilus-trading = { path = "crates/trading", version = "0.49.0" }

# Adapters
nautilus-blockchain = { path = "crates/adapters/blockchain", version = "0.49.0" }
nautilus-coinbase-intx = { path = "crates/adapters/coinbase_intx", version = "0.49.0" }
nautilus-databento = { path = "crates/adapters/databento", version = "0.49.0" }
nautilus-tardis = { path = "crates/adapters/tardis", version = "0.49.0" }

ahash = "0.8.12"
anyhow = "1.0.98"
arrow = "55.1.0"  # Keep major version in line with datafusion
async-stream = "0.3.6"
async-trait = "0.1.88"
base64 = "0.22.1"
bytes = { version = "1.10.1", features = ["serde"] }
chrono = { version = "0.4.41", features = ["serde"] }
chrono-tz = "0.10.3"
deranged = "=0.4.0"
derive_builder = "0.20.2"
enum_dispatch = "0.3.13"
futures = "0.3.31"
futures-util = "0.3.31"
heck = "0.5.0"
hex = "0.4.3"
indexmap = { version = "2.9.0", features = ["serde"] }
itertools = "0.14.0"
itoa = "1.0.15"
log = { version = "0.4.27", features = ["std", "kv_unstable", "serde", "release_max_level_debug"] }
memchr = "2.7.4"
parquet = "55.1.0"  # Keep major version in line with datafusion
pyo3 = { version = "0.24.2", features = ["chrono", "indexmap", "rust_decimal", "serde", "smallvec"] }
pyo3-async-runtimes = { version = "0.24.0", features = ["tokio-runtime", "tokio", "attributes"] }
rand = "0.9.1"
reqwest = { version = "0.12.18", features = ["blocking"] }
ring = "0.17.14"
rmp-serde = "1.3.0"
rust_decimal = "1.37.1"
rust_decimal_macros = "1.37.1"
rustls = { version = "0.23.27", features = ["ring"] }
semver = "1.0.25"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
serde_urlencoded = "0.7.1"
sqlx = { version = "0.8.6", features = ["postgres", "runtime-tokio", "json"] }
strum = { version = "0.27.1", features = ["derive"] }
thiserror = "2.0.12"
thousands = "0.2.0"
toml = "0.8.20"
tracing = "0.1.41"
# Disable default feature "tracing-log" since it interferes with custom logging
tracing-subscriber = { version = "0.3.19", default-features = false, features = ["smallvec", "fmt", "ansi", "std", "env-filter"] }
tokio = { version = "1.45.1", features = ["full"] }
tokio-tungstenite = { version = "0.26.2", features = ["rustls-tls-native-roots"] }
ustr = { version = "1.1.0", features = ["serde"] }
uuid = { version = "1.17.0", features = ["v4", "serde"] }

# dev-dependencies
axum = "0.8.4"
criterion = "0.5.1"
float-cmp = "0.10.0"
iai = "0.1.1"
pretty_assertions = "1.4.1"
proptest = "1.6.0"
rstest = "0.25.0"
tempfile = "3.20.0"
tracing-test = "0.2.5"
dotenvy = "0.15.7"

# build-dependencies
cbindgen = "0.29.0"

[profile.dev]
opt-level = 0
debug = false  # Improves compile times
debug-assertions = false  # Fails Cython build if true
overflow-checks = true
strip = "debuginfo"  # Improves compile times
lto = false
panic = "unwind"
incremental = true
codegen-units = 256

[profile.test]
opt-level = 0
debug = true
debug-assertions = true
overflow-checks = true
strip = false
lto = false
incremental = true
codegen-units = 256

[profile.nextest]
inherits = "test"
debug = false # Improves compile times
strip = "debuginfo" # Improves compile times

[profile.release]
opt-level = 3
debug = false
debug-assertions = false
overflow-checks = false
lto = true
strip = true
panic = "abort"
incremental = false
codegen-units = 1

[profile.release-debugging]
inherits = "release"
incremental = true
debug = "full"
strip = false
lto = false
